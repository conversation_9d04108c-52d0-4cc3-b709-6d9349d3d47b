"""
Locator Conflict Resolution for GretahAI ScriptWeaver

This module provides heuristic preprocessing to resolve conflicts between
step data locators and element matches data before sending to AI generation.

Key Features:
- Prioritizes manually selected elements over automated matches
- Uses reliability-based locator strategy ranking (NAME > CSS > XPATH > ID)
- Resolves conflicts by choosing highest confidence score with priority order
- Creates unified locator recommendations for AI consumption
- Comprehensive logging for debugging and transparency
- Extracts name attributes from CSS selectors and XPath expressions

The resolver eliminates AI confusion by preprocessing conflicting locator
information and providing a single, authoritative locator recommendation
following the priority order: name > css > xpath > id.
"""

import json
import time
import os
from typing import Dict, Any, List, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass
from debug_utils import debug

class LocatorStrategy(Enum):
    """Enum representing different locator strategies with their string values."""
    ID = "id"
    CSS = "css"
    CSS_SELECTOR = "css_selector"
    NAME = "name"
    CLASS = "class"
    XPATH = "xpath"
    TAG = "tag"
    TEXT = "text"
    URL = "url" 
    NONE = "none"
    
    @classmethod
    def from_string(cls, value: str) -> 'LocatorStrategy':
        """Convert string to enum member, defaulting to NONE for unknown values."""
        try:
            return cls(value.lower()) if value else cls.NONE
        except ValueError:
            return cls.NONE
            
    def __str__(self) -> str:
        return self.value

# Locator strategy reliability ranking (higher is better)
# Aligned with priority order: NAME > CSS > XPATH > ID
# This matches the _PREFERRED_ORDER = ["name", "css", "xpath", "id"]
LOCATOR_STRATEGY_RELIABILITY = {
    LocatorStrategy.NAME: 100,      # Highest priority - most stable
    LocatorStrategy.CSS: 80,        # Second priority - good balance
    LocatorStrategy.CSS_SELECTOR: 80,
    LocatorStrategy.XPATH: 60,      # Third priority - can be fragile
    LocatorStrategy.ID: 50,         # Fourth priority - can change
    LocatorStrategy.CLASS: 40,      # Lower priority - often dynamic
    LocatorStrategy.TAG: 30,        # Low priority - not specific
    LocatorStrategy.TEXT: 35,       # Low priority - can change
    LocatorStrategy.URL: 90,        # For navigation steps
    LocatorStrategy.NONE: 10        # Lowest priority
}

@dataclass
class ElementMatch:
    """Represents a matched element with its score and selection status."""
    element: Dict[str, Any]
    score: float
    manually_selected: bool = False
    
@dataclass
class ResolutionResult:
    """Represents the result of locator conflict resolution."""
    resolved_locator_strategy: str
    resolved_locator: str
    resolution_reason: str
    confidence_score: float
    original_step_locator: Dict[str, str]
    original_element_matches: List[Dict[str, Any]]
    conflict_detected: bool


# 1️⃣  preference table – put stable attributes first, empty IDs last
_PREFERRED_ORDER = ["name", "css", "xpath", "id"]

# Debug logging configuration
DEBUG_LOG_DIR = "logs/locator_resolution_debug"
DEBUG_ENABLED = True

def _log_comprehensive_debug(
    operation: str,
    test_case_id: str,
    step_no: str,
    data: Dict[str, Any],
    timestamp: str = None
) -> None:
    """
    Log comprehensive debug information for locator resolution troubleshooting.

    Args:
        operation: The operation being performed (input, processing, output)
        test_case_id: Test case identifier
        step_no: Step number
        data: Data to log
        timestamp: Optional timestamp (generated if not provided)
    """
    if not DEBUG_ENABLED:
        return

    try:
        if timestamp is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        # Create debug log directory if it doesn't exist
        os.makedirs(DEBUG_LOG_DIR, exist_ok=True)

        # Create unique log file for this resolution attempt and operation
        log_filename = f"locator_resolution_{test_case_id}_{step_no}_{timestamp}_{operation}.json"
        log_filepath = os.path.join(DEBUG_LOG_DIR, log_filename)

        # Prepare log entry
        log_entry = {
            "timestamp": timestamp,
            "test_case_id": test_case_id,
            "step_no": step_no,
            "operation": operation,
            "data": data
        }

        # Write to file
        with open(log_filepath, 'w', encoding='utf-8') as f:
            json.dump(log_entry, f, indent=2, default=str)

        # Also log to standard debug system
        debug(
            f"Comprehensive debug logged: {operation}",
            stage="locator_resolution_debug",
            operation=f"debug_log_{operation}",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "log_file": log_filename,
                "data_keys": list(data.keys()) if isinstance(data, dict) else "non_dict"
            }
        )

    except Exception as e:
        debug(
            f"Error writing comprehensive debug log: {e}",
            stage="locator_resolution_debug",
            operation="debug_log_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "operation": operation
            }
        )


def resolve_locator_conflicts(
    step_data: Dict[str, Any],
    element_matches: List[Dict[str, Any]],
    step_no: str,
    test_case_id: str = "unknown"
) -> Dict[str, Any]:
    """
    Resolve conflicts between step data locators and element matches.
    
    This function implements heuristic preprocessing to eliminate conflicting
    locator information before sending data to AI generation prompts.
    
    Args:
        step_data: Step data from JSON storage with locator_strategy and locator
        element_matches: List of element matches with selector, xpath, score, manually_selected
        step_no: Step number for logging context
        test_case_id: Test case ID for logging context
        
    Returns:
        Dict containing resolved locator information with:
        - resolved_locator_strategy: The chosen locator strategy
        - resolved_locator: The chosen locator value
        - resolution_reason: Explanation of why this locator was chosen
        - confidence_score: Confidence in the resolution (0.0-1.0)
        - original_step_locator: Original step data locator for reference
        - original_element_matches: Original element matches for reference
        - conflict_detected: Boolean indicating if conflicts were found
    """
    try:
        # Generate unique timestamp for this resolution attempt
        resolution_timestamp = time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        # 📊 COMPREHENSIVE INPUT LOGGING
        input_data = {
            "resolution_id": f"{test_case_id}_{step_no}_{resolution_timestamp}",
            "step_data": {
                "locator_strategy": step_data.get('locator_strategy', ''),
                "locator": step_data.get('locator', ''),
                "full_step_data": step_data
            },
            "element_matches": {
                "count": len(element_matches),
                "matches": []
            },
            "context": {
                "test_case_id": test_case_id,
                "step_no": step_no,
                "preferred_order": _PREFERRED_ORDER,
                "reliability_scores": {str(k): v for k, v in LOCATOR_STRATEGY_RELIABILITY.items()}
            }
        }

        # Log detailed element match data
        for i, match in enumerate(element_matches):
            element = match.get('element', {})
            match_data = {
                "index": i,
                "score": match.get('score', 0),
                "manually_selected": match.get('manually_selected', False),
                "element_data": {
                    "selector": element.get('selector', ''),
                    "xpath": element.get('xpath', ''),
                    "attributes": element.get('attributes', {}),
                    "all_element_keys": list(element.keys())
                },
                "raw_match": match
            }
            input_data["element_matches"]["matches"].append(match_data)

        _log_comprehensive_debug("INPUT", test_case_id, step_no, input_data, resolution_timestamp)

        debug(
            f"Starting locator conflict resolution for test case {test_case_id}, step {step_no}",
            stage="locator_resolution",
            operation="conflict_resolution_start",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "step_locator_strategy": step_data.get('locator_strategy', ''),
                "step_locator": step_data.get('locator', ''),
                "element_matches_count": len(element_matches),
                "resolution_id": input_data["resolution_id"]
            }
        )

        # ─────────────────────────────
        # 2️⃣  convert element data to locator format and scrub invalid locators
        # ─────────────────────────────
        processing_data = {
            "conversion_results": [],
            "filtered_matches": [],
            "conversion_summary": {
                "total_input": len(element_matches),
                "old_format_count": 0,
                "new_format_count": 0,
                "successful_conversions": 0,
                "failed_conversions": 0,
                "filtered_out": 0
            }
        }

        valid_matches = []
        for i, m in enumerate(element_matches):
            conversion_result = {
                "index": i,
                "input_match": m,
                "conversion_type": None,
                "output_match": None,
                "success": False,
                "reason": None
            }

            # Enhanced format detection: Always prioritize element analysis when element data is available
            # Check multiple possible locations for element data
            element = None
            if 'element_data' in m:
                element = m['element_data']
            elif 'element' in m:
                element = m['element']
            elif 'raw_match' in m and 'element' in m['raw_match']:
                element = m['raw_match']['element']
            else:
                element = {}

            has_element_data = bool(element and (element.get('attributes') or element.get('selector') or element.get('xpath')))
            has_old_format = 'locator_strategy' in m and 'locator' in m

            # Priority 1: Element analysis (when element data is available)
            if has_element_data:
                conversion_result["conversion_type"] = "element_analysis_priority"
                processing_data["conversion_summary"]["new_format_count"] += 1

                debug(
                    f"Prioritizing element analysis for element {i} (element data available)",
                    stage="locator_resolution",
                    operation="element_analysis_priority",
                    context={
                        "element_index": i,
                        "has_element_data": has_element_data,
                        "has_old_format": has_old_format,
                        "element_keys": list(element.keys()),
                        "attributes_available": bool(element.get('attributes')),
                        "name_attribute": element.get('attributes', {}).get('name'),
                        "original_strategy": m.get('locator_strategy'),
                        "original_locator": m.get('locator')
                    }
                )

                converted_match = _convert_element_to_locator_match(element, m, resolution_timestamp, test_case_id, step_no, i)

                if converted_match and converted_match.get('locator'):
                    # Element analysis successful - use the result
                    conversion_result["success"] = True
                    conversion_result["output_match"] = converted_match
                    conversion_result["reason"] = f"element_analysis_to_{converted_match.get('locator_strategy', 'unknown')}"
                    valid_matches.append(converted_match)
                    processing_data["conversion_summary"]["successful_conversions"] += 1

                    debug(
                        f"Element analysis successful for element {i} - selected {converted_match.get('locator_strategy')} strategy",
                        stage="locator_resolution",
                        operation="element_analysis_success",
                        context={
                            "element_index": i,
                            "converted_strategy": converted_match.get('locator_strategy'),
                            "converted_locator": converted_match.get('locator'),
                            "original_strategy": m.get('locator_strategy'),
                            "original_locator": m.get('locator'),
                            "priority_improvement": converted_match.get('locator_strategy') in _PREFERRED_ORDER and m.get('locator_strategy', '').lower() in _PREFERRED_ORDER and _PREFERRED_ORDER.index(converted_match.get('locator_strategy')) < _PREFERRED_ORDER.index(m.get('locator_strategy', '').lower()) if converted_match.get('locator_strategy') and m.get('locator_strategy') else False
                        }
                    )
                elif has_old_format:
                    # Element analysis failed but old format data is available - fallback to old format
                    conversion_result["conversion_type"] = "old_format_fallback"
                    conversion_result["reason"] = "element_analysis_failed_using_old_format"

                    loc = (m.get("locator") or "").strip()
                    strat = (m.get("locator_strategy") or "").lower()

                    if not loc:
                        conversion_result["reason"] = "empty_locator_after_element_analysis_failed"
                        processing_data["conversion_summary"]["filtered_out"] += 1
                    else:
                        conversion_result["success"] = True
                        conversion_result["output_match"] = m
                        valid_matches.append(m)
                        processing_data["conversion_summary"]["successful_conversions"] += 1

                        debug(
                            f"Element analysis failed for element {i} - falling back to old format {strat} strategy",
                            stage="locator_resolution",
                            operation="old_format_fallback",
                            context={
                                "element_index": i,
                                "fallback_strategy": strat,
                                "fallback_locator": loc,
                                "element_analysis_failed": True
                            }
                        )
                else:
                    # Both element analysis and old format failed
                    conversion_result["reason"] = "both_element_analysis_and_old_format_failed"
                    processing_data["conversion_summary"]["failed_conversions"] += 1

                    debug(
                        f"Both element analysis and old format failed for element {i}",
                        stage="locator_resolution",
                        operation="conversion_complete_failure",
                        context={
                            "element_index": i,
                            "element_data": element,
                            "has_old_format": has_old_format,
                            "failure_reason": "No valid locator found in either element data or old format"
                        }
                    )

            # Priority 2: Old format only (when no element data is available)
            elif has_old_format:
                conversion_result["conversion_type"] = "old_format_validation"
                processing_data["conversion_summary"]["old_format_count"] += 1

                loc = (m.get("locator") or "").strip()
                strat = (m.get("locator_strategy") or "").lower()

                if not loc:
                    conversion_result["reason"] = "empty_locator"
                    processing_data["conversion_summary"]["filtered_out"] += 1
                elif strat == "id" and loc == "":
                    conversion_result["reason"] = "empty_id_locator"
                    processing_data["conversion_summary"]["filtered_out"] += 1
                else:
                    conversion_result["success"] = True
                    conversion_result["output_match"] = m
                    conversion_result["reason"] = "old_format_valid"
                    valid_matches.append(m)
                    processing_data["conversion_summary"]["successful_conversions"] += 1

                    debug(
                        f"Using old format validation for element {i} (no element data available)",
                        stage="locator_resolution",
                        operation="old_format_only",
                        context={
                            "element_index": i,
                            "strategy": strat,
                            "locator": loc,
                            "reason": "no_element_data_available"
                        }
                    )

            # Priority 3: Neither element data nor old format available
            else:
                conversion_result["conversion_type"] = "no_valid_data"
                conversion_result["reason"] = "no_element_data_or_old_format"
                processing_data["conversion_summary"]["failed_conversions"] += 1

                debug(
                    f"No valid data found for element {i} - skipping",
                    stage="locator_resolution",
                    operation="no_valid_data",
                    context={
                        "element_index": i,
                        "has_element_data": has_element_data,
                        "has_old_format": has_old_format,
                        "match_keys": list(m.keys())
                    }
                )

            processing_data["conversion_results"].append(conversion_result)

        element_matches = valid_matches      # use the cleaned list

        # Log the processing results
        processing_data["final_valid_matches"] = element_matches
        _log_comprehensive_debug("PROCESSING_CONVERSION", test_case_id, step_no, processing_data, resolution_timestamp)

        debug(
            f"Completed locator validation - filtered {len(element_matches)} valid matches",
            stage="locator_resolution",
            operation="locator_validation",
            context={
                "valid_matches_count": len(element_matches),
                "test_case_id": test_case_id,
                "step_no": step_no
            }
        )

        # extract step-data locator *after* cleaning
        step_locator_strategy = (step_data or {}).get('locator_strategy', '').lower()
        step_locator          = (step_data or {}).get('locator', '').strip()

        # default result (fallback to step data)
        result = ResolutionResult(
            resolved_locator_strategy=step_locator_strategy,
            resolved_locator=step_locator,
            resolution_reason='No element matches available – using step data locator',
            confidence_score=0.8,
            original_step_locator={'strategy': step_locator_strategy, 'locator': step_locator},
            original_element_matches=element_matches,
            conflict_detected=False
        )

        # if still no element matches, keep step data BUT STILL LOG COMPREHENSIVE OUTPUT
        if not element_matches:
            debug(
                "No element matches after validation - using step data locator",
                stage="locator_resolution",
                operation="fallback_to_step_data",
                context={
                    "test_case_id": test_case_id,
                    "step_no": step_no,
                    "step_locator_strategy": step_locator_strategy,
                    "step_locator": step_locator
                }
            )

            # 📊 STILL LOG COMPREHENSIVE OUTPUT FOR EMPTY MATCHES CASE
            empty_scoring_data = {
                "candidates": [],
                "scoring_logic": {
                    "preferred_order": _PREFERRED_ORDER,
                    "base_score_multiplier": 10,
                    "unknown_strategy_base": 5,
                    "formula": "base_priority_score + element_score"
                },
                "selection_result": {
                    "error": "No valid candidates after element conversion",
                    "fallback_used": True,
                    "fallback_strategy": step_locator_strategy,
                    "fallback_locator": step_locator,
                    "fallback_reason": "All element conversions failed or no elements provided"
                }
            }
            _log_comprehensive_debug("PROCESSING_SCORING", test_case_id, step_no, empty_scoring_data, resolution_timestamp)

            # Log comprehensive output for fallback case
            fallback_output_data = {
                "final_result": {
                    "resolved_locator_strategy": result.resolved_locator_strategy,
                    "resolved_locator": result.resolved_locator,
                    "confidence_score": result.confidence_score,
                    "resolution_reason": result.resolution_reason,
                    "conflict_detected": result.conflict_detected
                },
                "comparison_with_input": {
                    "original_step_strategy": step_data.get('locator_strategy', ''),
                    "original_step_locator": step_data.get('locator', ''),
                    "strategy_changed": False,  # Using step data unchanged
                    "locator_changed": False,   # Using step data unchanged
                    "improvement_analysis": {
                        "fallback_reason": "No element matches after conversion",
                        "priority_logic_bypassed": True
                    }
                },
                "decision_summary": {
                    "total_candidates_evaluated": 0,
                    "selection_method": "step_data_fallback",
                    "priority_order_used": _PREFERRED_ORDER,
                    "why_this_choice": f"No valid element candidates - using step data {step_locator_strategy} strategy"
                },
                "fallback_analysis": {
                    "reason": "empty_element_matches_after_conversion",
                    "original_element_count": len(input_data["element_matches"]["matches"]),
                    "conversion_failures": processing_data["conversion_summary"]["failed_conversions"],
                    "filtered_out": processing_data["conversion_summary"]["filtered_out"]
                }
            }
            _log_comprehensive_debug("OUTPUT", test_case_id, step_no, fallback_output_data, resolution_timestamp)

            return vars(result)

        # ─────────────────────────────
        # 3️⃣  ALWAYS PROCESS ELEMENT MATCHES WHEN AVAILABLE
        #     Priority-based scoring to find the best locator strategy
        #     regardless of whether conflicts are detected
        # ─────────────────────────────

        debug(
            f"Processing {len(element_matches)} element matches for priority-based selection",
            stage="locator_resolution",
            operation="priority_processing_start",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "element_count": len(element_matches),
                "step_strategy": step_locator_strategy,
                "step_locator": step_locator
            }
        )

        # 📊 COMPREHENSIVE PRIORITY SCORING LOGGING
        scoring_data = {
            "candidates": [],
            "scoring_logic": {
                "preferred_order": _PREFERRED_ORDER,
                "base_score_multiplier": 10,
                "unknown_strategy_base": 5,
                "formula": "base_priority_score + element_score"
            },
            "selection_result": {}
        }

        ranked = []
        for i, m in enumerate(element_matches):
            strat = m["locator_strategy"].lower()
            loc   = m["locator"]
            element_score = m.get("score", 0)

            # Calculate priority-based score
            if strat in _PREFERRED_ORDER:
                priority_index = _PREFERRED_ORDER.index(strat)
                base_score = (len(_PREFERRED_ORDER) - priority_index) * 10
                priority_explanation = f"Priority {priority_index + 1} of {len(_PREFERRED_ORDER)} (higher is better)"
            else:
                base_score = 5
                priority_explanation = "Unknown strategy - low base score"

            total_score = base_score + element_score

            # Calculate reliability score for comparison
            element_data = m.get('element', {})
            reliability_score = _calculate_locator_reliability(element_data) if element_data else 0

            candidate_info = {
                "index": i,
                "strategy": strat,
                "locator": loc,
                "element_score": element_score,
                "base_score": base_score,
                "total_score": total_score,
                "reliability_score": reliability_score,
                "priority_explanation": priority_explanation,
                "manually_selected": m.get('manually_selected', False),
                "element_attributes": element_data.get('attributes', {}) if element_data else {},
                "full_match": m
            }

            scoring_data["candidates"].append(candidate_info)
            ranked.append((total_score, strat, loc, m))

        # Find the best candidate from element matches
        if ranked:
            score, strat, loc, raw = max(ranked, key=lambda x: x[0])

            # Find the selected candidate in our detailed data
            selected_candidate = None
            for candidate in scoring_data["candidates"]:
                if (candidate["total_score"] == score and
                    candidate["strategy"] == strat and
                    candidate["locator"] == loc):
                    selected_candidate = candidate
                    break

            # Compare element-based result with step data
            step_score = 0
            if step_locator_strategy in _PREFERRED_ORDER:
                step_priority_index = _PREFERRED_ORDER.index(step_locator_strategy)
                step_score = (len(_PREFERRED_ORDER) - step_priority_index) * 10 + 8  # Add 8 for step data reliability

            # Choose the better option: element analysis vs step data
            use_element_result = score > step_score

            if use_element_result:
                scoring_data["selection_result"] = {
                    "selected_strategy": strat,
                    "selected_locator": loc,
                    "selected_score": score,
                    "confidence_score": score / 100.0,
                    "selected_candidate": selected_candidate,
                    "alternatives_count": len(ranked) - 1,
                    "conflict_detected": len(ranked) > 1 or score != step_score,
                    "selection_reason": f"{strat} preferred (element analysis score {score} vs step data score {step_score})",
                    "comparison_with_step_data": {
                        "step_strategy": step_locator_strategy,
                        "step_locator": step_locator,
                        "step_score": step_score,
                        "element_score": score,
                        "element_preferred": True,
                        "score_difference": score - step_score
                    }
                }

                result.resolved_locator_strategy = strat
                result.resolved_locator = loc
                result.confidence_score = score / 100.0
                result.resolution_reason = f"{strat} preferred (element analysis score {score} vs step data score {step_score})"
                result.conflict_detected = len(ranked) > 1 or score != step_score

                debug(
                    f"Element analysis result preferred over step data",
                    stage="locator_resolution",
                    operation="element_preferred",
                    context={
                        "element_strategy": strat,
                        "element_score": score,
                        "step_strategy": step_locator_strategy,
                        "step_score": step_score,
                        "score_improvement": score - step_score
                    }
                )
            else:
                # Step data is better or equal - use step data but log the comparison
                scoring_data["selection_result"] = {
                    "selected_strategy": step_locator_strategy,
                    "selected_locator": step_locator,
                    "selected_score": step_score,
                    "confidence_score": step_score / 100.0,
                    "selected_candidate": None,
                    "alternatives_count": len(ranked),
                    "conflict_detected": False,
                    "selection_reason": f"{step_locator_strategy} preferred (step data score {step_score} vs element analysis score {score})",
                    "comparison_with_step_data": {
                        "step_strategy": step_locator_strategy,
                        "step_locator": step_locator,
                        "step_score": step_score,
                        "element_score": score,
                        "element_preferred": False,
                        "score_difference": step_score - score
                    }
                }

                # Keep the default result (step data)
                result.resolution_reason = f"{step_locator_strategy} preferred (step data score {step_score} vs element analysis score {score})"
                result.confidence_score = step_score / 100.0

                debug(
                    f"Step data preferred over element analysis result",
                    stage="locator_resolution",
                    operation="step_data_preferred",
                    context={
                        "step_strategy": step_locator_strategy,
                        "step_score": step_score,
                        "element_strategy": strat,
                        "element_score": score,
                        "score_difference": step_score - score
                    }
                )

            # Add alternatives to the selection result if element result was chosen
            if use_element_result:
                alternatives = [r for r in ranked if r != (score, strat, loc, raw)]
                if alternatives:
                    scoring_data["selection_result"]["alternatives"] = []
                    for alt_score, alt_strat, alt_loc, alt_raw in alternatives:
                        alt_info = {
                            "strategy": alt_strat,
                            "locator": alt_loc,
                            "score": alt_score,
                            "score_difference": score - alt_score,
                            "why_not_selected": f"Lower total score ({alt_score} vs {score})"
                        }
                        scoring_data["selection_result"]["alternatives"].append(alt_info)

                # Also add step data as an alternative if it was considered
                if step_score > 0:
                    step_alternative = {
                        "strategy": step_locator_strategy,
                        "locator": step_locator,
                        "score": step_score,
                        "score_difference": score - step_score,
                        "why_not_selected": f"Lower step data score ({step_score} vs {score})"
                    }
                    if "alternatives" not in scoring_data["selection_result"]:
                        scoring_data["selection_result"]["alternatives"] = []
                    scoring_data["selection_result"]["alternatives"].append(step_alternative)
        else:
            scoring_data["selection_result"] = {
                "error": "No valid candidates found",
                "fallback_used": True
            }

        # Log the scoring analysis
        _log_comprehensive_debug("PROCESSING_SCORING", test_case_id, step_no, scoring_data, resolution_timestamp)

        # 📊 COMPREHENSIVE OUTPUT LOGGING
        output_data = {
            "final_result": {
                "resolved_locator_strategy": result.resolved_locator_strategy,
                "resolved_locator": result.resolved_locator,
                "confidence_score": result.confidence_score,
                "resolution_reason": result.resolution_reason,
                "conflict_detected": result.conflict_detected
            },
            "comparison_with_input": {
                "original_step_strategy": step_data.get('locator_strategy', ''),
                "original_step_locator": step_data.get('locator', ''),
                "strategy_changed": result.resolved_locator_strategy != step_data.get('locator_strategy', '').lower(),
                "locator_changed": result.resolved_locator != step_data.get('locator', ''),
                "improvement_analysis": {}
            },
            "decision_summary": {
                "total_candidates_evaluated": len(element_matches),
                "selection_method": "priority_based_scoring",
                "priority_order_used": _PREFERRED_ORDER,
                "why_this_choice": f"Selected {result.resolved_locator_strategy} strategy with confidence {result.confidence_score:.3f}"
            }
        }

        # Analyze if this is an improvement over the original
        original_strategy = step_data.get('locator_strategy', '').lower()
        if original_strategy and original_strategy in _PREFERRED_ORDER and result.resolved_locator_strategy in _PREFERRED_ORDER:
            original_priority = _PREFERRED_ORDER.index(original_strategy)
            new_priority = _PREFERRED_ORDER.index(result.resolved_locator_strategy)

            output_data["comparison_with_input"]["improvement_analysis"] = {
                "original_priority_index": original_priority,
                "new_priority_index": new_priority,
                "priority_improved": new_priority < original_priority,  # Lower index = higher priority
                "priority_explanation": f"Changed from priority {original_priority + 1} to {new_priority + 1}"
            }

        # Add specific analysis for the original issue (CSS low confidence)
        if (result.resolved_locator_strategy == 'name' and
            original_strategy == 'css' and
            result.confidence_score > 0.31):
            output_data["original_issue_analysis"] = {
                "issue_resolved": True,
                "description": "Successfully prioritized name-based locator over low-confidence CSS",
                "confidence_improvement": result.confidence_score - 0.31,
                "strategy_improvement": "css -> name"
            }

        _log_comprehensive_debug("OUTPUT", test_case_id, step_no, output_data, resolution_timestamp)

        debug(
            f"Successfully resolved locator conflict - selected '{result.resolved_locator_strategy}' strategy",
            stage="locator_resolution",
            operation="strategy_selection",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "resolved_strategy": result.resolved_locator_strategy,
                "resolved_locator": result.resolved_locator,
                "confidence_score": result.confidence_score,
                "conflict_detected": result.conflict_detected,
                "candidates_evaluated": len(element_matches),
                "resolution_id": input_data["resolution_id"]
            }
        )
        return vars(result)

    except Exception as e:
        # Extract step data safely for error handling
        safe_step_strategy = (step_data or {}).get('locator_strategy', '') if 'step_data' in locals() else ''
        safe_step_locator = (step_data or {}).get('locator', '') if 'step_data' in locals() else ''
        safe_element_matches = element_matches if 'element_matches' in locals() else []
        safe_resolution_timestamp = resolution_timestamp if 'resolution_timestamp' in locals() else time.strftime("%Y%m%d_%H%M%S") + f"_{int(time.time() * 1000) % 1000:03d}"

        debug(
            f"Error during locator conflict resolution - using fallback strategy",
            stage="locator_resolution",
            operation="error_fallback",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "error_line": str(e.__traceback__.tb_lineno) if e.__traceback__ else "unknown",
                "fallback_strategy": "css",
                "fallback_locator": "input:not([type='hidden'])"
            }
        )

        # Log comprehensive error analysis
        error_output_data = {
            "final_result": {
                "resolved_locator_strategy": "css",
                "resolved_locator": "input:not([type='hidden'])",
                "confidence_score": 0.0,
                "resolution_reason": f"Fallback due to {type(e).__name__}: {e}",
                "conflict_detected": True
            },
            "error_analysis": {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "error_line": str(e.__traceback__.tb_lineno) if e.__traceback__ else "unknown",
                "fallback_used": True,
                "original_step_strategy": safe_step_strategy,
                "original_step_locator": safe_step_locator,
                "element_matches_count": len(safe_element_matches)
            },
            "decision_summary": {
                "total_candidates_evaluated": 0,
                "selection_method": "error_fallback",
                "priority_order_used": _PREFERRED_ORDER,
                "why_this_choice": f"Exception occurred during resolution: {type(e).__name__}"
            }
        }
        _log_comprehensive_debug("OUTPUT", test_case_id, step_no, error_output_data, safe_resolution_timestamp)
        return vars(ResolutionResult(
            resolved_locator_strategy="css",
            resolved_locator="input:not([type='hidden'])",
            resolution_reason=f"Fallback due to {type(e).__name__}: {e}",
            confidence_score=0.0,
            original_step_locator={'strategy': safe_step_strategy, 'locator': safe_step_locator},
            original_element_matches=safe_element_matches,
            conflict_detected=True
        ))


def _detect_locator_conflicts(step_data: Dict[str, Any], element_matches: List[Dict[str, Any]]) -> bool:
    """
    Detect if there are conflicts between step data and element matches.
    
    Args:
        step_data: Step data with locator information
        element_matches: List of element matches
        
    Returns:
        bool: True if conflicts are detected, False otherwise
    """
    try:
        step_locator = step_data.get('locator', '').strip()
        step_strategy = step_data.get('locator_strategy', '').lower().strip()
        
        # No conflict if step has no locator or is navigation
        if not step_locator or step_strategy in ['url', 'none', '']:
            return False
            
        # No conflict if no element matches
        if not element_matches:
            return False
            
        # Check if any element match has different selector information
        for match in element_matches:
            element = match.get('element', {})
            element_selector = element.get('selector', '')
            element_xpath = element.get('xpath', '')
            
            # Determine element's implied strategy
            element_strategy = 'css' if element_selector else 'xpath' if element_xpath else 'none'
            
            # Check for strategy mismatch (NEW)
            if element_strategy != step_strategy and step_strategy not in ['', 'none']:
                debug(
                    f"Strategy conflict detected between step and element",
                    stage="locator_resolution",
                    operation="conflict_detection",
                    context={
                        "step_strategy": step_strategy,
                        "element_strategy": element_strategy,
                        "step_locator": step_locator,
                        "element_selector": element_selector,
                        "element_xpath": element_xpath
                    }
                )
                return True

            # If element has selector/xpath that differs from step locator, it's a conflict
            if element_selector and element_selector != step_locator:
                debug(
                    f"Locator value conflict detected - step vs element selector",
                    stage="locator_resolution",
                    operation="conflict_detection",
                    context={
                        "step_locator": step_locator,
                        "element_selector": element_selector,
                        "conflict_type": "selector_mismatch"
                    }
                )
                return True

            if element_xpath and element_xpath != step_locator:
                debug(
                    f"Locator value conflict detected - step vs element xpath",
                    stage="locator_resolution",
                    operation="conflict_detection",
                    context={
                        "step_locator": step_locator,
                        "element_xpath": element_xpath,
                        "conflict_type": "xpath_mismatch"
                    }
                )
                return True

        return False

    except Exception as e:
        debug(
            f"Error during conflict detection - assuming no conflicts",
            stage="locator_resolution",
            operation="conflict_detection_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_result": False
            }
        )
        return False


def _convert_element_to_locator_match(
    element: Dict[str, Any],
    original_match: Dict[str, Any],
    resolution_timestamp: str = None,
    test_case_id: str = "unknown",
    step_no: str = "unknown",
    element_index: int = -1
) -> Dict[str, Any]:
    """
    Convert element data to locator match format using priority logic.

    This function implements the priority order: name > css > xpath > id
    and converts element data to the expected locator_strategy/locator format.

    Args:
        element: Element data with selector, xpath, and attributes
        original_match: Original match data (for score, manually_selected, etc.)

    Returns:
        Dict with locator_strategy and locator fields, or None if no valid locator
    """
    try:
        selector = element.get('selector', '')
        xpath = element.get('xpath', '')
        attributes = element.get('attributes', {})

        # 📊 DETAILED CONVERSION LOGGING
        conversion_analysis = {
            "element_index": element_index,
            "input_data": {
                "selector": selector,
                "xpath": xpath,
                "attributes": attributes,
                "original_score": original_match.get('score', 0),
                "manually_selected": original_match.get('manually_selected', False)
            },
            "priority_checks": [],
            "selected_strategy": None,
            "selected_locator": None,
            "selection_reason": None
        }

        # Priority 1: NAME attribute (highest priority)
        name_attr = attributes.get('name')
        conversion_analysis["priority_checks"].append({
            "priority": 1,
            "strategy": "name",
            "check": "direct_name_attribute",
            "available": bool(name_attr),
            "value": name_attr,
            "selected": bool(name_attr)
        })

        if name_attr:
            result = {
                'locator_strategy': 'name',
                'locator': name_attr,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = name_attr
            conversion_analysis["selection_reason"] = "Direct name attribute found (highest priority)"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 2: CSS selector with name attribute
        css_name_available = selector and '[name=' in selector
        css_name_value = None
        if css_name_available:
            import re
            name_match = re.search(r'\[name=["\']([^"\']+)["\']', selector)
            css_name_value = name_match.group(1) if name_match else None

        conversion_analysis["priority_checks"].append({
            "priority": 2,
            "strategy": "name",
            "check": "css_selector_name_attribute",
            "available": css_name_available,
            "value": css_name_value,
            "selector": selector,
            "selected": bool(css_name_value)
        })

        if css_name_value:
            result = {
                'locator_strategy': 'name',
                'locator': css_name_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = css_name_value
            conversion_analysis["selection_reason"] = f"Name extracted from CSS selector: {selector}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 3: CSS selector (general)
        css_available = bool(selector)
        css_is_id = selector.startswith('#') if selector else False
        css_strategy = 'id' if css_is_id else 'css'
        css_locator = selector[1:] if css_is_id else selector

        conversion_analysis["priority_checks"].append({
            "priority": 3,
            "strategy": css_strategy,
            "check": "css_selector_general",
            "available": css_available,
            "value": css_locator,
            "is_id_selector": css_is_id,
            "raw_selector": selector,
            "selected": css_available and not css_name_value  # Only if name wasn't found
        })

        if css_available and not css_name_value:  # Only if name wasn't already found
            result = {
                'locator_strategy': css_strategy,
                'locator': css_locator,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = css_strategy
            conversion_analysis["selected_locator"] = css_locator
            conversion_analysis["selection_reason"] = f"CSS selector ({'ID' if css_is_id else 'general'}): {selector}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 4: XPath with name attribute
        xpath_name_available = xpath and '@name=' in xpath
        xpath_name_value = None
        if xpath_name_available:
            import re
            name_match = re.search(r'@name=["\']([^"\']+)["\']', xpath)
            xpath_name_value = name_match.group(1) if name_match else None

        conversion_analysis["priority_checks"].append({
            "priority": 4,
            "strategy": "name",
            "check": "xpath_name_attribute",
            "available": xpath_name_available,
            "value": xpath_name_value,
            "xpath": xpath,
            "selected": bool(xpath_name_value)
        })

        if xpath_name_value:
            result = {
                'locator_strategy': 'name',
                'locator': xpath_name_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "name"
            conversion_analysis["selected_locator"] = xpath_name_value
            conversion_analysis["selection_reason"] = f"Name extracted from XPath: {xpath}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 5: XPath (general)
        xpath_available = bool(xpath)
        xpath_is_id = xpath and '@id=' in xpath
        xpath_id_value = None
        xpath_strategy = None
        xpath_locator = None

        if xpath_available:
            if xpath_is_id:
                import re
                id_match = re.search(r'@id=["\']([^"\']+)["\']', xpath)
                xpath_id_value = id_match.group(1) if id_match else None
                xpath_strategy = 'id'
                xpath_locator = xpath_id_value
            else:
                xpath_strategy = 'xpath'
                xpath_locator = xpath

        conversion_analysis["priority_checks"].append({
            "priority": 5,
            "strategy": xpath_strategy or "xpath",
            "check": "xpath_general",
            "available": xpath_available,
            "value": xpath_locator,
            "is_id_xpath": xpath_is_id,
            "raw_xpath": xpath,
            "selected": xpath_available and not css_name_value and not xpath_name_value
        })

        if xpath_available and not css_name_value and not xpath_name_value:
            result = {
                'locator_strategy': xpath_strategy,
                'locator': xpath_locator,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = xpath_strategy
            conversion_analysis["selected_locator"] = xpath_locator
            conversion_analysis["selection_reason"] = f"XPath ({'ID-based' if xpath_is_id else 'general'}): {xpath}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # Priority 6: ID attribute (lowest priority)
        id_attr_available = bool(attributes.get('id'))
        id_attr_value = attributes.get('id')

        conversion_analysis["priority_checks"].append({
            "priority": 6,
            "strategy": "id",
            "check": "id_attribute",
            "available": id_attr_available,
            "value": id_attr_value,
            "selected": id_attr_available and not css_name_value and not xpath_name_value and not css_available and not xpath_available
        })

        if id_attr_available and not css_name_value and not xpath_name_value and not css_available and not xpath_available:
            result = {
                'locator_strategy': 'id',
                'locator': id_attr_value,
                'element': element,
                'score': original_match.get('score', 0),
                'manually_selected': original_match.get('manually_selected', False)
            }
            conversion_analysis["selected_strategy"] = "id"
            conversion_analysis["selected_locator"] = id_attr_value
            conversion_analysis["selection_reason"] = f"ID attribute (lowest priority): {id_attr_value}"

            if resolution_timestamp:
                _log_comprehensive_debug(
                    f"ELEMENT_CONVERSION_{element_index}",
                    test_case_id,
                    step_no,
                    conversion_analysis,
                    resolution_timestamp
                )

            return result

        # No valid locator found - log comprehensive analysis
        conversion_analysis["selected_strategy"] = None
        conversion_analysis["selected_locator"] = None
        conversion_analysis["selection_reason"] = "No valid locator found in any priority check"

        if resolution_timestamp:
            _log_comprehensive_debug(
                f"ELEMENT_CONVERSION_{element_index}_FAILED",
                test_case_id,
                step_no,
                conversion_analysis,
                resolution_timestamp
            )

        debug(
            f"No valid locator found for element",
            stage="locator_resolution",
            operation="element_conversion_failed",
            context={
                "element_keys": list(element.keys()),
                "has_selector": bool(selector),
                "has_xpath": bool(xpath),
                "has_attributes": bool(attributes),
                "element_index": element_index,
                "priority_checks_performed": len(conversion_analysis["priority_checks"])
            }
        )
        return None

    except Exception as e:
        debug(
            f"Error converting element to locator match",
            stage="locator_resolution",
            operation="element_conversion_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
        )
        return None


def generate_debug_summary_report(test_case_id: str = None, step_no: str = None) -> Dict[str, Any]:
    """
    Generate a summary report of all locator resolution debug logs.

    Args:
        test_case_id: Optional filter by test case ID
        step_no: Optional filter by step number

    Returns:
        Dict containing summary analysis of debug logs
    """
    try:
        if not os.path.exists(DEBUG_LOG_DIR):
            return {"error": "No debug logs found", "log_directory": DEBUG_LOG_DIR}

        log_files = [f for f in os.listdir(DEBUG_LOG_DIR) if f.endswith('.json')]

        # Filter by test case and step if specified
        if test_case_id:
            log_files = [f for f in log_files if test_case_id in f]
        if step_no:
            log_files = [f for f in log_files if f"_{step_no}_" in f]

        summary = {
            "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_log_files": len(log_files),
            "filters_applied": {
                "test_case_id": test_case_id,
                "step_no": step_no
            },
            "resolution_attempts": [],
            "patterns": {
                "name_strategy_selected": 0,
                "css_strategy_selected": 0,
                "xpath_strategy_selected": 0,
                "id_strategy_selected": 0,
                "low_confidence_css_issues": 0,
                "successful_name_prioritization": 0
            },
            "issues_found": []
        }

        for log_file in log_files:
            try:
                with open(os.path.join(DEBUG_LOG_DIR, log_file), 'r', encoding='utf-8') as f:
                    log_data = json.load(f)

                if log_data.get("operation") == "OUTPUT":
                    resolution_data = log_data.get("data", {})
                    final_result = resolution_data.get("final_result", {})

                    strategy = final_result.get("resolved_locator_strategy", "unknown")
                    confidence = final_result.get("confidence_score", 0)

                    # Count strategy selections
                    if strategy == "name":
                        summary["patterns"]["name_strategy_selected"] += 1
                    elif strategy == "css":
                        summary["patterns"]["css_strategy_selected"] += 1
                    elif strategy == "xpath":
                        summary["patterns"]["xpath_strategy_selected"] += 1
                    elif strategy == "id":
                        summary["patterns"]["id_strategy_selected"] += 1

                    # Check for low confidence CSS issues
                    if strategy == "css" and confidence < 0.4:
                        summary["patterns"]["low_confidence_css_issues"] += 1
                        summary["issues_found"].append({
                            "type": "low_confidence_css",
                            "file": log_file,
                            "strategy": strategy,
                            "confidence": confidence,
                            "description": f"CSS strategy selected with low confidence ({confidence:.3f})"
                        })

                    # Check for successful name prioritization
                    comparison = resolution_data.get("comparison_with_input", {})
                    if (comparison.get("original_step_strategy") == "css" and
                        strategy == "name"):
                        summary["patterns"]["successful_name_prioritization"] += 1

                    summary["resolution_attempts"].append({
                        "file": log_file,
                        "test_case_id": log_data.get("test_case_id"),
                        "step_no": log_data.get("step_no"),
                        "strategy": strategy,
                        "confidence": confidence,
                        "original_issue_resolved": resolution_data.get("original_issue_analysis", {}).get("issue_resolved", False)
                    })

            except Exception as e:
                summary["issues_found"].append({
                    "type": "log_parsing_error",
                    "file": log_file,
                    "error": str(e),
                    "description": f"Failed to parse log file: {e}"
                })

        return summary

    except Exception as e:
        return {
            "error": f"Failed to generate summary report: {e}",
            "error_type": type(e).__name__
        }


def _apply_resolution_heuristics(
    step_data: Dict[str, Any],
    element_matches: List[Dict[str, Any]],
    step_no: str,
    test_case_id: str
) -> Dict[str, Any]:
    """
    Apply heuristic rules to resolve locator conflicts.

    Priority order:
    1. Manually selected elements (manually_selected: true)
    2. Highest confidence score element
    3. Most reliable locator strategy (NAME > CSS > XPATH > ID)
    4. Step data as fallback

    Args:
        step_data: Step data with locator information
        element_matches: List of element matches
        step_no: Step number for logging
        test_case_id: Test case ID for logging

    Returns:
        Dict with resolved locator information
    """
    try:
        # Convert to ElementMatch objects for easier handling
        typed_matches = [
            ElementMatch(
                element=match.get('element', {}),
                score=match.get('score', 0),
                manually_selected=match.get('manually_selected', False)
            )
            for match in element_matches
        ]
        
        # Rule 1: Check for manually selected elements - get highest scoring one if multiple
        manually_selected = [match for match in typed_matches if match.manually_selected]

        if manually_selected:
            debug(
                f"Found manually selected elements - applying highest priority rule",
                stage="locator_resolution",
                operation="manual_selection_priority",
                context={
                    "test_case_id": test_case_id,
                    "step_no": step_no,
                    "manually_selected_count": len(manually_selected),
                    "total_matches": len(typed_matches)
                }
            )

            # If multiple manual selections, pick highest score
            if len(manually_selected) > 1:
                best_match = max(manually_selected, key=lambda m: m.score)
                debug(
                    f"Selected highest scoring manually selected element",
                    stage="locator_resolution",
                    operation="manual_selection_scoring",
                    context={
                        "test_case_id": test_case_id,
                        "step_no": step_no,
                        "selected_score": best_match.score,
                        "candidates_count": len(manually_selected)
                    }
                )
            else:
                best_match = manually_selected[0]

            return _create_resolution_result(
                best_match,
                "Manually selected element (highest priority)",
                1.0  # Perfect confidence for manual selection
            )

        # Rule 2 & 3: Single scan to find highest score and most reliable locator
        if typed_matches:
            highest_score = max(match.score for match in typed_matches)
            high_score_threshold = max(0.7, highest_score * 0.9)  # Within 10% of highest or 0.7 minimum
            
            # Filter to high-scoring matches
            high_score_matches = [
                match for match in typed_matches
                if match.score >= high_score_threshold
            ]
            
            debug(
                f"Evaluating high-scoring elements for reliability",
                stage="locator_resolution",
                operation="reliability_evaluation",
                context={
                    "test_case_id": test_case_id,
                    "step_no": step_no,
                    "high_score_matches_count": len(high_score_matches),
                    "score_threshold": high_score_threshold,
                    "highest_score": highest_score
                }
            )

            # Evaluate locator strategy reliability for high-scoring matches in one pass
            best_match = None
            best_reliability = -1

            for match in high_score_matches:
                reliability = _calculate_locator_reliability(match.element)
                if reliability > best_reliability:
                    best_reliability = reliability
                    best_match = match

            if best_match:
                confidence = min(0.95, best_match.score + 0.1)  # Boost confidence slightly
                debug(
                    f"Selected best match based on reliability and score",
                    stage="locator_resolution",
                    operation="best_match_selection",
                    context={
                        "test_case_id": test_case_id,
                        "step_no": step_no,
                        "selected_score": best_match.score,
                        "reliability_score": best_reliability,
                        "confidence": confidence
                    }
                )
                return _create_resolution_result(
                    best_match,
                    f"Highest confidence element with reliable locator strategy (score: {best_match.score:.2f})",
                    confidence
                )

        # Rule 4: Fallback to step data
        debug(
            f"No suitable element matches found - falling back to step data",
            stage="locator_resolution",
            operation="step_data_fallback",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "reason": "no_suitable_matches"
            }
        )
        step_locator_strategy = step_data.get('locator_strategy', LocatorStrategy.NONE.value)
        step_locator = step_data.get('locator', '')

        return {
            'resolved_locator_strategy': step_locator_strategy,
            'resolved_locator': step_locator,
            'resolution_reason': 'Fallback to step data - no suitable element matches found',
            'confidence_score': 0.6
        }

    except Exception as e:
        debug(
            f"Error during resolution heuristics - using step data fallback",
            stage="locator_resolution",
            operation="heuristics_error_fallback",
            context={
                "test_case_id": test_case_id,
                "step_no": step_no,
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
        )
        # Safe fallback - use original strategy or NONE
        original_strategy = step_data.get('locator_strategy', '')
        return {
            'resolved_locator_strategy': original_strategy if original_strategy else LocatorStrategy.NONE.value,
            'resolved_locator': step_data.get('locator', ''),
            'resolution_reason': f'Error in heuristics - fallback to step data: {str(e)}',
            'confidence_score': 0.5
        }


def _calculate_locator_reliability(element: Dict[str, Any]) -> int:
    """
    Calculate reliability score for an element's locator strategies.

    Implements proper priority order: name > css > xpath > id
    Returns the highest reliability score based on available locator strategies.

    Args:
        element: Element data with selector, xpath, and attributes

    Returns:
        int: Reliability score (higher is better)
    """
    try:
        attributes = element.get('attributes', {})
        selector = element.get('selector', '')
        xpath = element.get('xpath', '')

        # Priority 1: NAME attribute (highest reliability)
        if attributes.get('name'):
            debug(
                f"Element has name attribute - highest reliability",
                stage="locator_resolution",
                operation="reliability_name_priority",
                context={
                    "name_value": attributes['name'],
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Fast path: Check for data-test* attributes (very high reliability)
        if any(k.startswith('data-test') for k in attributes.keys()):
            return 90  # Very high reliability for test attributes

        # Priority 2: Check if CSS selector contains name attribute
        if selector and '[name=' in selector:
            debug(
                f"CSS selector contains name attribute - high reliability",
                stage="locator_resolution",
                operation="reliability_css_name",
                context={
                    "selector": selector,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Priority 3: Check if XPath contains name attribute
        if xpath and '@name=' in xpath:
            debug(
                f"XPath contains name attribute - high reliability",
                stage="locator_resolution",
                operation="reliability_xpath_name",
                context={
                    "xpath": xpath,
                    "reliability_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5
                }
            )
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5

        # Priority 4: ID attribute
        if attributes.get('id'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]

        # Evaluate other CSS selector types
        max_score = 0
        if selector:
            css_score = _evaluate_css_selector_reliability(selector)
            max_score = max(max_score, css_score)

        # Evaluate XPath reliability
        if xpath:
            xpath_score = _evaluate_xpath_reliability(xpath)
            max_score = max(max_score, xpath_score)

        return max_score

    except Exception as e:
        debug(
            f"Error calculating locator reliability - returning default score",
            stage="locator_resolution",
            operation="reliability_calculation_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": 0
            }
        )
        return 0


def _evaluate_css_selector_reliability(selector: str) -> int:
    """
    Evaluate the reliability of a CSS selector.

    Prioritizes name-based selectors according to the priority order.

    Args:
        selector: CSS selector string

    Returns:
        int: Reliability score
    """
    try:
        if not selector:
            return 0

        # Highest priority: Name-based attribute selectors
        if '[name=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME]

        # Fast path: data-test* attributes are highly reliable
        if '[data-testid=' in selector or '[data-test=' in selector:
            return 90

        # ID selectors - lower priority than name
        if selector.startswith('#'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID]

        # ID-based attribute selectors
        if '[id=' in selector:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 5

        # Class selectors are moderately reliable
        if '.' in selector and not selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 10

        if selector.startswith('.'):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CLASS]

        # Tag selectors are less reliable
        if selector.isalpha():
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TAG]

        # Complex selectors get moderate score
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] - 20

    except Exception as e:
        debug(
            f"Error evaluating CSS selector reliability - returning default score",
            stage="locator_resolution",
            operation="css_reliability_evaluation_error",
            context={
                "selector": selector,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] // 2
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.CSS] // 2


def _evaluate_xpath_reliability(xpath: str) -> int:
    """
    Evaluate the reliability of an XPath selector.

    Prioritizes name-based XPath according to the priority order.

    Args:
        xpath: XPath selector string

    Returns:
        int: Reliability score
    """
    try:
        if not xpath:
            return 0

        # Highest priority: Name-based XPath
        if '@name=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.NAME] - 5  # Slightly lower than direct name attribute

        # Fast path: data-test* attributes in XPath
        if '@data-testid=' in xpath or '@data-test=' in xpath:
            return 85

        # ID-based XPath - lower priority than name
        if '@id=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.ID] - 10

        # Text-based XPath is moderately reliable
        if 'text()=' in xpath:
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.TEXT]

        # Position-based XPath is less reliable
        if '[' in xpath and ']' in xpath and any(char.isdigit() for char in xpath):
            return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] - 20

        # Generic XPath
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH]

    except Exception as e:
        debug(
            f"Error evaluating XPath reliability - returning default score",
            stage="locator_resolution",
            operation="xpath_reliability_evaluation_error",
            context={
                "xpath": xpath,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_score": LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] // 2
            }
        )
        return LOCATOR_STRATEGY_RELIABILITY[LocatorStrategy.XPATH] // 2


def _create_resolution_result(
    element_match: Union[ElementMatch, Dict[str, Any]],
    reason: str,
    confidence: float
) -> Dict[str, Any]:
    """
    Create a standardized resolution result from an element match.

    Implements proper priority order: name > css > xpath > id
    This follows the _PREFERRED_ORDER configuration.

    Args:
        element_match: The selected element match
        reason: Reason for selection
        confidence: Confidence score (0.0-1.0)

    Returns:
        Dict with resolved locator information
    """
    try:
        # Handle both ElementMatch and dictionary inputs
        if isinstance(element_match, ElementMatch):
            element = element_match.element
        else:
            element = element_match.get('element', {})

        # Get available locator options
        selector = element.get('selector', '')
        xpath = element.get('xpath', '')
        attributes = element.get('attributes', {})

        # Implement proper priority order: name > css > xpath > id
        strategy = LocatorStrategy.NONE.value
        locator = ''
        priority_reason = ''

        # Priority 1: NAME attribute (highest priority)
        if attributes.get('name'):
            strategy = LocatorStrategy.NAME.value
            locator = attributes['name']
            priority_reason = 'name attribute (highest priority)'

        # Priority 2: CSS selector (but check if it's name-based first)
        elif selector:
            if '[name=' in selector:
                # Extract name value from CSS selector like input[name="username"]
                import re
                name_match = re.search(r'\[name=["\']([^"\']+)["\']', selector)
                if name_match:
                    strategy = LocatorStrategy.NAME.value
                    locator = name_match.group(1)
                    priority_reason = 'name from CSS selector (highest priority)'
                else:
                    strategy = LocatorStrategy.CSS.value
                    locator = selector
                    priority_reason = 'CSS selector (second priority)'
            elif selector.startswith('#'):
                # ID selector - lower priority than name but higher than generic CSS
                strategy = LocatorStrategy.ID.value
                locator = selector[1:]  # Remove # prefix for ID
                priority_reason = 'ID from CSS selector (fourth priority)'
            else:
                strategy = LocatorStrategy.CSS.value
                locator = selector
                priority_reason = 'CSS selector (second priority)'

        # Priority 3: XPath (but check if it's name-based first)
        elif xpath:
            if '@name=' in xpath:
                # Extract name value from XPath like //input[@name='username']
                import re
                name_match = re.search(r'@name=["\']([^"\']+)["\']', xpath)
                if name_match:
                    strategy = LocatorStrategy.NAME.value
                    locator = name_match.group(1)
                    priority_reason = 'name from XPath (highest priority)'
                else:
                    strategy = LocatorStrategy.XPATH.value
                    locator = xpath
                    priority_reason = 'XPath selector (third priority)'
            elif '@id=' in xpath:
                # ID-based XPath - lower priority than name
                import re
                id_match = re.search(r'@id=["\']([^"\']+)["\']', xpath)
                if id_match:
                    strategy = LocatorStrategy.ID.value
                    locator = id_match.group(1)
                    priority_reason = 'ID from XPath (fourth priority)'
                else:
                    strategy = LocatorStrategy.XPATH.value
                    locator = xpath
                    priority_reason = 'XPath selector (third priority)'
            else:
                strategy = LocatorStrategy.XPATH.value
                locator = xpath
                priority_reason = 'XPath selector (third priority)'

        # Priority 4: ID attribute (lowest priority)
        elif attributes.get('id'):
            strategy = LocatorStrategy.ID.value
            locator = attributes['id']
            priority_reason = 'ID attribute (fourth priority)'

        # Log the priority-based selection
        debug(
            f"Locator strategy selected using priority order",
            stage="locator_resolution",
            operation="priority_selection",
            context={
                "selected_strategy": strategy,
                "selected_locator": locator,
                "priority_reason": priority_reason,
                "available_options": {
                    "name_attr": bool(attributes.get('name')),
                    "css_selector": bool(selector),
                    "xpath": bool(xpath),
                    "id_attr": bool(attributes.get('id'))
                }
            }
        )

        return {
            'resolved_locator_strategy': strategy,
            'resolved_locator': locator,
            'resolution_reason': f"{reason} - {priority_reason}",
            'confidence_score': confidence
        }

    except Exception as e:
        debug(
            f"Error creating resolution result - using fallback",
            stage="locator_resolution",
            operation="result_creation_error",
            context={
                "error_type": type(e).__name__,
                "error_message": str(e),
                "fallback_strategy": LocatorStrategy.NONE.value,
                "fallback_confidence": 0.3
            }
        )
        return {
            'resolved_locator_strategy': LocatorStrategy.NONE.value,
            'resolved_locator': '',
            'resolution_reason': f'Error creating result - using fallback: {str(e)}',
            'confidence_score': 0.3
        }
